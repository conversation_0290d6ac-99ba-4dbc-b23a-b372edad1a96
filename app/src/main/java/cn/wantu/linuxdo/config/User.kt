package cn.wantu.linuxdo.config

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BriefUser(
    @SerialName("animated_avatar")
    val animatedAvatar: String?,
    @SerialName("avatar_template")
    val avatarTemplate: String,
    @SerialName("id")
    val id: Int,
    @SerialName("name")
    val name: String,
    @SerialName("trust_level")
    val trustLevel: Int,
    @SerialName("username")
    val username: String
)


@Serializable
data class Self(
    @SerialName("accepted_answers")
    val acceptedAnswers: Int,
    @SerialName("admin")
    val admin: Boolean,
    @SerialName("allow_people_to_follow_me")
    val allowPeopleToFollowMe: Boolean,
//    @SerialName("allowed_pm_usernames")
//    val allowedPmUsernames: List<Any?>,
    @SerialName("animated_avatar")
    val animatedAvatar: String?,
//    @SerialName("associated_accounts")
//    val associatedAccounts: List<Any?>,
    @SerialName("avatar_template")
    val avatarTemplate: String,
    @SerialName("badge_count")
    val badgeCount: Int,
    @SerialName("birthdate")
    val birthdate: String?,
    @SerialName("cakedate")
    val cakedate: String,
    @SerialName("can_change_bio")
    val canChangeBio: Boolean,
    @SerialName("can_change_location")
    val canChangeLocation: Boolean,
    @SerialName("can_change_tracking_preferences")
    val canChangeTrackingPreferences: Boolean,
    @SerialName("can_change_website")
    val canChangeWebsite: Boolean,
    @SerialName("can_chat_user")
    val canChatUser: Boolean,
    @SerialName("can_edit")
    val canEdit: Boolean,
    @SerialName("can_edit_email")
    val canEditEmail: Boolean,
    @SerialName("can_edit_name")
    val canEditName: Boolean,
    @SerialName("can_edit_username")
    val canEditUsername: Boolean,
    @SerialName("can_follow")
    val canFollow: Boolean,
    @SerialName("can_ignore_user")
    val canIgnoreUser: Boolean,
    @SerialName("can_ignore_users")
    val canIgnoreUsers: Boolean,
    @SerialName("can_mute_user")
    val canMuteUser: Boolean,
    @SerialName("can_mute_users")
    val canMuteUsers: Boolean,
    @SerialName("can_pick_theme_with_custom_homepage")
    val canPickThemeWithCustomHomepage: Boolean,
    @SerialName("can_see_followers")
    val canSeeFollowers: Boolean,
    @SerialName("can_see_following")
    val canSeeFollowing: Boolean,
    @SerialName("can_see_network_tab")
    val canSeeNetworkTab: Boolean,
    @SerialName("can_send_private_message_to_user")
    val canSendPrivateMessageToUser: Boolean,
    @SerialName("can_send_private_messages")
    val canSendPrivateMessages: Boolean,
    @SerialName("can_upload_profile_header")
    val canUploadProfileHeader: Boolean,
    @SerialName("can_upload_user_card_background")
    val canUploadUserCardBackground: Boolean,
    @SerialName("can_use_saved_searches")
    val canUseSavedSearches: Boolean,
//    @SerialName("card_badge")
//    val cardBadge: Any?,
//    @SerialName("card_image_badge")
//    val cardImageBadge: Any?,
//    @SerialName("card_image_badge_id")
//    val cardImageBadgeId: Any?,
//    @SerialName("category_expert_endorsements")
//    val categoryExpertEndorsements: Any?,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("custom_fields")
    val customFields: CustomFields,
    @SerialName("display_sidebar_tags")
    val displaySidebarTags: Boolean,
    @SerialName("email")
    val email: String,
    @SerialName("featured_topic")
//    val featuredTopic: Any?,
//    @SerialName("featured_user_badge_ids")
    val featuredUserBadgeIds: List<Int>,
//    @SerialName("flair_bg_color")
//    val flairBgColor: Any?,
//    @SerialName("flair_color")
//    val flairColor: Any?,
//    @SerialName("flair_group_id")
//    val flairGroupId: Any?,
//    @SerialName("flair_name")
//    val flairName: Any?,
//    @SerialName("flair_url")
//    val flairUrl: Any?,
    @SerialName("gamification_score")
    val gamificationScore: Int,
    @SerialName("gravatar_avatar_template")
    val gravatarAvatarTemplate: String,
    @SerialName("gravatar_avatar_upload_id")
    val gravatarAvatarUploadId: Int,
    @SerialName("group_users")
    val groupUsers: List<GroupUser>,
    @SerialName("groups")
    val groups: List<Group>,
    @SerialName("has_title_badges")
    val hasTitleBadges: Boolean,
    @SerialName("id")
    val id: Int,
    @SerialName("ignored")
    val ignored: Boolean,
//    @SerialName("ignored_usernames")
//    val ignoredUsernames: List<Any?>,
//    @SerialName("invited_by")
//    val invitedBy: Any?,
    @SerialName("is_followed")
    val isFollowed: Boolean,
    @SerialName("last_posted_at")
    val lastPostedAt: String,
    @SerialName("last_seen_at")
    val lastSeenAt: String,
    @SerialName("locale")
    val locale: String,
    @SerialName("mailing_list_posts_per_day")
    val mailingListPostsPerDay: Int,
    @SerialName("moderator")
    val moderator: Boolean,
    @SerialName("muted")
    val muted: Boolean,
    @SerialName("muted_category_ids")
    val mutedCategoryIds: List<Int>,
//    @SerialName("muted_tags")
//    val mutedTags: List<Any?>,
//    @SerialName("muted_usernames")
//    val mutedUsernames: List<Any?>,
    @SerialName("name")
    val name: String,
    @SerialName("notify_followed_user_when_followed")
    val notifyFollowedUserWhenFollowed: Boolean,
    @SerialName("notify_me_when_followed")
    val notifyMeWhenFollowed: Boolean,
    @SerialName("notify_me_when_followed_creates_topic")
    val notifyMeWhenFollowedCreatesTopic: Boolean,
    @SerialName("notify_me_when_followed_replies")
    val notifyMeWhenFollowedReplies: Boolean,
    @SerialName("pending_count")
    val pendingCount: Int,
    @SerialName("pending_posts_count")
    val pendingPostsCount: Int,
//    @SerialName("primary_group_id")
//    val primaryGroupId: Any?,
//    @SerialName("primary_group_name")
//    val primaryGroupName: Any?,
    @SerialName("profile_view_count")
    val profileViewCount: Int,
    @SerialName("recent_time_read")
    val recentTimeRead: Int,
//    @SerialName("regular_category_ids")
//    val regularCategoryIds: List<Any?>,
    @SerialName("reminders_frequency")
    val remindersFrequency: List<RemindersFrequency>,
//    @SerialName("saved_searches")
//    val savedSearches: List<Any?>,
    @SerialName("second_factor_backup_enabled")
    val secondFactorBackupEnabled: Boolean,
    @SerialName("second_factor_enabled")
    val secondFactorEnabled: Boolean,
//    @SerialName("secondary_emails")
//    val secondaryEmails: List<Any?>,
    @SerialName("see_signatures")
    val seeSignatures: Boolean,
    @SerialName("sidebar_category_ids")
    val sidebarCategoryIds: List<Int>,
    @SerialName("sidebar_tags")
    val sidebarTags: List<SidebarTag>,
    @SerialName("system_avatar_template")
    val systemAvatarTemplate: String,
//    @SerialName("system_avatar_upload_id")
//    val systemAvatarUploadId: Any?,
    @SerialName("time_read")
    val timeRead: Int,
    @SerialName("timezone")
    val timezone: String,
//    @SerialName("title")
//    val title: Any?,
    @SerialName("total_followers")
    val totalFollowers: Int,
    @SerialName("total_following")
    val totalFollowing: Int,
//    @SerialName("tracked_category_ids")
//    val trackedCategoryIds: List<Any?>,
//    @SerialName("tracked_tags")
//    val trackedTags: List<Any?>,
    @SerialName("trust_level")
    val trustLevel: Int,
//    @SerialName("unconfirmed_emails")
//    val unconfirmedEmails: List<Any?>,
    @SerialName("uploaded_avatar_id")
    val uploadedAvatarId: Int,
    @SerialName("use_logo_small_as_avatar")
    val useLogoSmallAsAvatar: Boolean,
//    @SerialName("user_api_keys")
//    val userApiKeys: Any?,
    @SerialName("user_auth_tokens")
    val userAuthTokens: List<UserAuthToken>,
    @SerialName("user_fields")
    val userFields: UserFields,
    @SerialName("user_notification_schedule")
    val userNotificationSchedule: UserNotificationSchedule,
    @SerialName("user_option")
    val userOption: UserOption,
//    @SerialName("user_passkeys")
//    val userPasskeys: List<Any?>,
    @SerialName("username")
    val username: String,
    @SerialName("vote_count")
    val voteCount: Int,
//    @SerialName("watched_category_ids")
//    val watchedCategoryIds: List<Any?>,
//    @SerialName("watched_first_post_category_ids")
//    val watchedFirstPostCategoryIds: List<Any?>,
//    @SerialName("watched_tags")
//    val watchedTags: List<Any?>,
//    @SerialName("watching_first_post_tags")
//    val watchingFirstPostTags: List<Any?>
)

@Serializable
class CustomFields

@Serializable
data class GroupUser(
    @SerialName("group_id")
    val groupId: Int,
    @SerialName("notification_level")
    val notificationLevel: Int,
    @SerialName("owner")
    val owner: Boolean,
    @SerialName("user_id")
    val userId: Int
)

@Serializable
data class Group(
    @SerialName("allow_membership_requests")
    val allowMembershipRequests: Boolean,
    @SerialName("automatic")
    val automatic: Boolean,
    @SerialName("bio_cooked")
    val bioCooked: String,
    @SerialName("bio_excerpt")
    val bioExcerpt: String,
    @SerialName("can_see_members")
    val canSeeMembers: Boolean,
    @SerialName("default_notification_level")
    val defaultNotificationLevel: Int,
    @SerialName("display_name")
    val displayName: String,
    @SerialName("flair_bg_color")
    val flairBgColor: String,
    @SerialName("flair_color")
    val flairColor: String,
//    @SerialName("flair_url")
//    val flairUrl: Any?,
//    @SerialName("full_name")
//    val fullName: Any?,
//    @SerialName("grant_trust_level")
//    val grantTrustLevel: Any?,
    @SerialName("has_messages")
    val hasMessages: Boolean,
    @SerialName("id")
    val id: Int,
    @SerialName("members_visibility_level")
    val membersVisibilityLevel: Int,
//    @SerialName("membership_request_template")
//    val membershipRequestTemplate: Any?,
    @SerialName("mentionable_level")
    val mentionableLevel: Int,
    @SerialName("messageable_level")
    val messageableLevel: Int,
    @SerialName("name")
    val name: String,
    @SerialName("primary_group")
    val primaryGroup: Boolean,
    @SerialName("public_admission")
    val publicAdmission: Boolean,
    @SerialName("public_exit")
    val publicExit: Boolean,
    @SerialName("publish_read_state")
    val publishReadState: Boolean,
//    @SerialName("title")
//    val title: Any?,
    @SerialName("user_count")
    val userCount: Int,
    @SerialName("visibility_level")
    val visibilityLevel: Int
)

@Serializable
data class RemindersFrequency(
    @SerialName("name")
    val name: String,
    @SerialName("value")
    val value: Int
)

@Serializable
data class SidebarTag(
//    @SerialName("description")
//    val description: Any?,
    @SerialName("name")
    val name: String,
    @SerialName("pm_only")
    val pmOnly: Boolean
)

@Serializable
data class UserAuthToken(
    @SerialName("browser")
    val browser: String,
    @SerialName("client_ip")
    val clientIp: String,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("device")
    val device: String,
    @SerialName("icon")
    val icon: String,
    @SerialName("id")
    val id: Int,
    @SerialName("is_active")
    val isActive: Boolean,
    @SerialName("location")
    val location: String,
    @SerialName("os")
    val os: String,
    @SerialName("seen_at")
    val seenAt: String
)

@Serializable
data class UserFields(
    @SerialName("3")
    val x3: String?,
    @SerialName("4")
    val x4: String?
)

@Serializable
data class UserNotificationSchedule(
    @SerialName("day_0_end_time")
    val day0EndTime: Int,
    @SerialName("day_0_start_time")
    val day0StartTime: Int,
    @SerialName("day_1_end_time")
    val day1EndTime: Int,
    @SerialName("day_1_start_time")
    val day1StartTime: Int,
    @SerialName("day_2_end_time")
    val day2EndTime: Int,
    @SerialName("day_2_start_time")
    val day2StartTime: Int,
    @SerialName("day_3_end_time")
    val day3EndTime: Int,
    @SerialName("day_3_start_time")
    val day3StartTime: Int,
    @SerialName("day_4_end_time")
    val day4EndTime: Int,
    @SerialName("day_4_start_time")
    val day4StartTime: Int,
    @SerialName("day_5_end_time")
    val day5EndTime: Int,
    @SerialName("day_5_start_time")
    val day5StartTime: Int,
    @SerialName("day_6_end_time")
    val day6EndTime: Int,
    @SerialName("day_6_start_time")
    val day6StartTime: Int,
    @SerialName("enabled")
    val enabled: Boolean
)

@Serializable
data class UserOption(
    @SerialName("allow_private_messages")
    val allowPrivateMessages: Boolean,
    @SerialName("auto_track_topics_after_msecs")
    val autoTrackTopicsAfterMsecs: Int,
    @SerialName("automatically_unpin_topics")
    val automaticallyUnpinTopics: Boolean,
    @SerialName("bookmark_auto_delete_preference")
    val bookmarkAutoDeletePreference: Int,
    @SerialName("chat_email_frequency")
    val chatEmailFrequency: String,
    @SerialName("chat_enabled")
    val chatEnabled: Boolean,
    @SerialName("chat_header_indicator_preference")
    val chatHeaderIndicatorPreference: String,
    @SerialName("chat_quick_reaction_type")
    val chatQuickReactionType: String,
//    @SerialName("chat_quick_reactions_custom")
//    val chatQuickReactionsCustom: Any?,
    @SerialName("chat_send_shortcut")
    val chatSendShortcut: String,
    @SerialName("chat_separate_sidebar_mode")
    val chatSeparateSidebarMode: String,
//    @SerialName("color_scheme_id")
//    val colorSchemeId: Any?,
//    @SerialName("dark_scheme_id")
//    val darkSchemeId: Any?,
    @SerialName("default_calendar")
    val defaultCalendar: String,
    @SerialName("digest_after_minutes")
    val digestAfterMinutes: Int,
    @SerialName("dynamic_favicon")
    val dynamicFavicon: Boolean,
    @SerialName("email_digests")
    val emailDigests: Boolean,
    @SerialName("email_in_reply_to")
    val emailInReplyTo: Boolean,
    @SerialName("email_level")
    val emailLevel: Int,
    @SerialName("email_messages_level")
    val emailMessagesLevel: Int,
    @SerialName("email_previous_replies")
    val emailPreviousReplies: Int,
    @SerialName("enable_allowed_pm_users")
    val enableAllowedPmUsers: Boolean,
    @SerialName("enable_defer")
    val enableDefer: Boolean,
    @SerialName("enable_quoting")
    val enableQuoting: Boolean,
    @SerialName("enable_smart_lists")
    val enableSmartLists: Boolean,
    @SerialName("external_links_in_new_tab")
    val externalLinksInNewTab: Boolean,
    @SerialName("hide_presence")
    val hidePresence: Boolean,
    @SerialName("hide_profile")
    val hideProfile: Boolean,
    @SerialName("hide_profile_and_presence")
    val hideProfileAndPresence: Boolean,
//    @SerialName("homepage_id")
//    val homepageId: Any?,
//    @SerialName("ignore_channel_wide_mention")
//    val ignoreChannelWideMention: Any?,
    @SerialName("include_tl0_in_digests")
    val includeTl0InDigests: Boolean,
    @SerialName("like_notification_frequency")
    val likeNotificationFrequency: Int,
    @SerialName("mailing_list_mode")
    val mailingListMode: Boolean,
    @SerialName("mailing_list_mode_frequency")
    val mailingListModeFrequency: Int,
    @SerialName("new_topic_duration_minutes")
    val newTopicDurationMinutes: Int,
    @SerialName("notification_level_when_assigned")
    val notificationLevelWhenAssigned: String,
    @SerialName("notification_level_when_replying")
    val notificationLevelWhenReplying: Int,
//    @SerialName("oldest_search_log_date")
//    val oldestSearchLogDate: Any?,
//    @SerialName("only_chat_push_notifications")
//    val onlyChatPushNotifications: Any?,
    @SerialName("policy_email_frequency")
    val policyEmailFrequency: String,
    @SerialName("seen_popups")
    val seenPopups: List<Int>,
    @SerialName("show_thread_title_prompts")
    val showThreadTitlePrompts: Boolean,
    @SerialName("sidebar_link_to_filtered_list")
    val sidebarLinkToFilteredList: Boolean,
    @SerialName("sidebar_show_count_of_new_items")
    val sidebarShowCountOfNewItems: Boolean,
    @SerialName("skip_new_user_tips")
    val skipNewUserTips: Boolean,
    @SerialName("text_size")
    val textSize: String,
    @SerialName("text_size_seq")
    val textSizeSeq: Int,
    @SerialName("theme_ids")
    val themeIds: List<Int>,
    @SerialName("theme_key_seq")
    val themeKeySeq: Int,
    @SerialName("timezone")
    val timezone: String,
    @SerialName("title_count_mode")
    val titleCountMode: String,
    @SerialName("topics_unread_when_closed")
    val topicsUnreadWhenClosed: Boolean,
    @SerialName("user_id")
    val userId: Int,
//    @SerialName("watched_precedence_over_muted")
//    val watchedPrecedenceOverMuted: Any?
)
