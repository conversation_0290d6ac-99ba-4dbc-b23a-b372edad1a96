package cn.wantu.linuxdo.config

import cn.wantu.linuxdo.network.CommonHeader
import cn.wantu.linuxdo.network.OkHttpClientSingleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.FormBody
import okhttp3.Request
import org.json.JSONObject


const val endpoint = "https://linux.do"

suspend fun getCsrf() = withContext(Dispatchers.IO) {
    val request = Request.Builder().url("$endpoint/session/csrf").get().build()
    OkHttpClientSingleton.getClient().newCall(request).execute().use { response ->
        response.body?.let {
            CommonHeader.setCsrf(JSONObject(it.string()).getString("csrf"))
        }
    }
    CommonHeader.csrf
}
suspend fun login(account: String, password: String) = withContext(Dispatchers.IO){
    val formBody = FormBody.Builder()
        .add("login", account)
        .add("password", password)
        .add("second_factor_method", "1")
        .add("timezone", "Asia/Shanghai")
        .build()
    val request = Request.Builder().url("$endpoint/session")
        .post(formBody)
        .build()
    val response = OkHttpClientSingleton.getClient().newCall(request).execute()
    Pair(response.code, response.body?.string())
}