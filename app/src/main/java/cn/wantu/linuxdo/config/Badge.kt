package cn.wantu.linuxdo.config

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class Badge(
    @SerialName("allow_title")
    val allowTitle: <PERSON><PERSON><PERSON>,
    @SerialName("badge_grouping_id")
    val badgeGroupingId: Int,
    @SerialName("badge_type_id")
    val badgeTypeId: Int,
    @SerialName("description")
    val description: String,
    @SerialName("enabled")
    val enabled: <PERSON>ole<PERSON>,
    @SerialName("grant_count")
    val grantCount: Int,
    @SerialName("icon")
    val icon: String,
    @SerialName("id")
    val id: Int,
    @SerialName("image_url")
    val imageUrl: String?,
    @SerialName("listable")
    val listable: <PERSON><PERSON><PERSON>,
    @SerialName("manually_grantable")
    val manuallyGrantable: Boolean,
    @SerialName("multiple_grant")
    val multipleGrant: <PERSON><PERSON><PERSON>,
    @SerialName("name")
    val name: String,
    @SerialName("show_in_post_header")
    val showInPostHeader: <PERSON><PERSON><PERSON>,
    @SerialName("slug")
    val slug: String,
    @SerialName("system")
    val system: Boolean
)

@Serializable
data class UserBadge(
    @SerialName("badge_id")
    val badgeId: Int,
    @SerialName("count")
    val count: Int,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("granted_at")
    val grantedAt: String,
    @SerialName("granted_by_id")
    val grantedById: Int,
    @SerialName("id")
    val id: Int,
    @SerialName("user_id")
    val userId: Int
)

@Serializable
data class BadgeType(
    @SerialName("id")
    val id: Int,
    @SerialName("name")
    val name: String,
    @SerialName("sort_order")
    val sortOrder: Int
)
