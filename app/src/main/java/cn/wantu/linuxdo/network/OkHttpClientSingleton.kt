package cn.wantu.linuxdo.network


import android.content.Context
import android.util.Log
import android.webkit.WebSettings
import com.franmontiel.persistentcookiejar.PersistentCookieJar
import com.franmontiel.persistentcookiejar.cache.SetCookieCache
import com.franmontiel.persistentcookiejar.persistence.SharedPrefsCookiePersistor
import okhttp3.Cache
import okhttp3.OkHttpClient
import okhttp3.Protocol
import okhttp3.logging.HttpLoggingInterceptor
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * OkHttpClient 的单例，用于在整个应用程序中提供一个共享的 OkHttpClient 实例。
 * 使用 Kotlin 的 object 关键字实现，确保了线程安全的单例。
 */
object OkHttpClientSingleton {

    private const val TAG = "OkHttpClientSingleton"

    // 使用 lateinit var，表示该变量会在使用前被初始化，避免在声明时就赋值（因为需要 Context）
    private lateinit var okHttpClient: OkHttpClient

    /**
     * 检查 OkHttpClient 是否已经被初始化。
     */
    val isInitialized: Boolean
        get() = this::okHttpClient.isInitialized // Kotlin 提供的机制，检查 lateinit 变量是否已初始化



    /**
     * 初始化 OkHttpClient 实例。
     * 必须在首次尝试获取客户端之前调用此方法，通常在 Application 的 onCreate() 中调用。
     *
     * @param context 应用程序上下文，用于创建缓存目录。
     */
    fun init(context: Context) {
        if (!isInitialized) {
            Log.d(TAG, "Building OkHttpClient instance...")

            // 1. 设置缓存
            val cacheDir = File(context.cacheDir, "okhttp_cache") // 缓存目录
            val cacheSize = 10 * 1024 * 1024L // 缓存大小：10 MB
            val cache = Cache(cacheDir, cacheSize)

            // 2. 设置日志拦截器 (仅在调试模式下启用，生产环境应避免输出敏感信息)
            val loggingInterceptor = HttpLoggingInterceptor().apply {
                // 设置日志级别：
                // NONE: 不打印日志
                // BASIC: 打印请求行和响应行
                // HEADERS: 打印请求和响应头
                // BODY: 打印请求和响应体 (最详细，用于调试)
                level = HttpLoggingInterceptor.Level.BODY
            }

            val cookieJar =
                PersistentCookieJar(SetCookieCache(), SharedPrefsCookiePersistor(context))
            // 3. 构建 OkHttpClient 实例
            okHttpClient = OkHttpClient.Builder()
                .cookieJar(cookieJar)
                .connectTimeout(30, TimeUnit.SECONDS) // 连接超时 30 秒
                .readTimeout(30, TimeUnit.SECONDS)    // 读取超时 30 秒
                .writeTimeout(30, TimeUnit.SECONDS)   // 写入超时 30 秒
                .cache(cache)                         // 添加缓存
                .addInterceptor(loggingInterceptor)   // 添加日志拦截器
                .protocols(listOf(Protocol.HTTP_2, Protocol.HTTP_1_1))
                // 可以添加其他拦截器，例如添加通用 Header 等
                .addInterceptor(CommonHeader(WebSettings.getDefaultUserAgent(context)))
                .build()

            Log.d(TAG, "OkHttpClient built successfully.")
        } else {
            Log.d(TAG, "OkHttpClient already initialized.")
        }
    }

    /**
     * 获取 OkHttpClient 实例。
     * 在调用此方法之前，请确保已经通过 init() 方法初始化了客户端。
     *
     * @return OkHttpClient 实例。
     * @throws IllegalStateException 如果客户端尚未初始化。
     */
    fun getClient(): OkHttpClient {
        if (!isInitialized) {
            throw IllegalStateException("OkHttpClient has not been initialized. Call OkHttpClientSingleton.init() first.")
        }
        return okHttpClient
    }
}