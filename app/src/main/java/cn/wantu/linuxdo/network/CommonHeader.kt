package cn.wantu.linuxdo.network

import okhttp3.Interceptor
import okhttp3.Interceptor.Chain
import okhttp3.Response
import kotlin.isInitialized

class CommonHeader(private val ua: String): Interceptor {
    companion object{
        var csrf = ""
            private set

        fun setCsrf(csrf: String){
            Companion.csrf = csrf
        }

    }

    override fun intercept(chain: Chain): Response {
        val originalRequest = chain.request()
        val requestBuilder = originalRequest.newBuilder()
            .header("x-requested-with", "XMLHttpRequest")
            .header("request-id", System.currentTimeMillis().toString())
            .header("user-agent", ua)
//            .header("user-agent", "Mozilla/5.0 (Linux; Android 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36")
        if (csrf != ""){
            requestBuilder.header("x-csrf-token", csrf)
        }
        return chain.proceed(requestBuilder.build())
    }


}