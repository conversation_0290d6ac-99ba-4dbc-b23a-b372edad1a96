package cn.wantu.linuxdo.ui.model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import cn.wantu.linuxdo.config.login
import cn.wantu.linuxdo.ui.view.ErrorNotification
import cn.wantu.linuxdo.ui.view.Notification
import cn.wantu.linuxdo.ui.view.SuccessNotification
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import org.json.JSONObject

class ForumLoginViewModel : ViewModel() {

    // 用户名或邮箱输入框的状态
    var username by mutableStateOf("")
        private set

    // 密码输入框的状态
    var password by mutableStateOf("")
        private set

    var showPassword by mutableStateOf(false)
        private set

    // “记住我”复选框的状态
    var rememberMe by mutableStateOf(false)
        private set

    // 登录按钮的加载状态
    var isLoading by mutableStateOf(false)
        private set

    // 修改为通知队列
    private val _notifications = mutableStateListOf<Notification>()
    val notifications: List<Notification> get() = _notifications // 暴露为不可变列表

    // 私有方法：添加通知到队列
    private fun addNotification(message: String, isError: Boolean = true) {
        _notifications.add(if (isError) ErrorNotification(message) else SuccessNotification(message))
    }

    // 公开方法：移除通知（供UI调用）
    fun dismissNotification(notification: Notification) {
        _notifications.remove(notification)
    }

    fun togglePasswordVisibility() {
        showPassword = !showPassword
    }

    // 更新用户名/邮箱
    fun onUsernameChange(newUsername: String) {
        username = newUsername
    }

    // 更新密码
    fun onPasswordChange(newPassword: String) {
        password = newPassword
    }

    // 切换“记住我”状态
    fun onRememberMeChange(checked: Boolean) {
        rememberMe = checked
    }

    // 处理登录逻辑
    fun onLoginClick() {

        if (username.isBlank()) {
            addNotification("用户名/邮箱不能为空")
            return
        }
        if (password.isBlank()) {
            addNotification("密码不能为空")
            return
        }
        if (password.length < 6) {
            addNotification("密码至少需要6位")
            return
        }

        isLoading = true // 显示加载状态

        viewModelScope.launch(Dispatchers.IO) {
            val (code, body) = login(username, password)
            if (code == 200) {
                // process 信息
                try {
                    addNotification(JSONObject(body!!).getString("error"))
                }catch (e: Exception){
                    addNotification("登录成功！欢迎回来。", false) // 实际应用中会导航到主页
                    // 这里可以保存rememberMe状态到SharedPreferences或DataStore
                    val j = Json { ignoreUnknownKeys = true }
                    j.decodeFromString(body!!)
                }


            } else {
                addNotification("未知错误")
            }
            isLoading = false // 隐藏加载状态
        }
    }

    // 模拟处理“忘记密码”
    fun onForgotPasswordClick() {
        addNotification("忘记密码功能开发中...")
        // 实际应用中会导航到忘记密码页面
    }

    // 模拟处理“注册”
    fun onRegisterClick() {
        addNotification("注册功能开发中...")
        // 实际应用中会导航到注册页面
    }
}