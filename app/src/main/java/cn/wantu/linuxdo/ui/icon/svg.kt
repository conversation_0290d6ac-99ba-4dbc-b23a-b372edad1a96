package cn.wantu.linuxdo.ui.icon


import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview

/**
 * 将SVG转换为Jetpack Compose ImageVector。
 * 这个版本是手动将SVG路径命令转换为PathBuilder函数。
 * 强烈建议对复杂SVG使用 PathData(String) 来解析 'd' 属性，因为它更简洁、不易出错。
 */
val MyManuallyConvertedSvgIcon: ImageVector
    get() {
        if (_myManuallyConvertedSvgIcon != null) {
            return _myManuallyConvertedSvgIcon!!
        }

        _myManuallyConvertedSvgIcon = ImageVector.Builder(
            name = "MyManuallyConvertedSvgIcon",
            defaultWidth = 174.276.dp, // 从SVG的width属性
            defaultHeight = 38.708.dp, // 从SVG的height属性
            viewportWidth = 174.276f, // 从SVG的viewBox属性 (第三个值)
            viewportHeight = 38.708f // 从SVG的viewBox属性 (第四个值)
        ).apply {
            path(
                // 颜色和描边属性从SVG的<g>元素中获取
                fill = SolidColor(Color(0xFF000000)), // fill="#000000"
                stroke = SolidColor(Color(0xFF000000)), // stroke="#000000"
                strokeLineWidth = 0.25f, // stroke-width="0.25mm" -> 0.25f (Compose使用浮点数，单位与viewport一致)
                strokeLineCap = StrokeCap.Round, // stroke-linecap="round"
                pathFillType = PathFillType.EvenOdd // fill-rule="evenodd"
            ) {
                // 开始解析SVG的d属性
                // M 8.009 26.903
                moveTo(8.009f, 26.903f)
                // L 8.709 20.553
                lineTo(8.709f, 20.553f)
                // A 80.704 80.704 0 0 1 4.122 21.813
                arcTo(80.704f, 80.704f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 4.122f, 21.813f)
                // A 215.147 215.147 0 0 1 1.909 22.353
                arcTo(215.147f, 215.147f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 1.909f, 22.353f)
                // L 1.909 18.603
                lineTo(1.909f, 18.603f)
                // Q 5.659 17.753 9.109 16.703
                quadTo(5.659f, 17.753f, 9.109f, 16.703f)
                // L 10.109 8.553
                lineTo(10.109f, 8.553f)
                // A 10.228 10.228 0 0 1 12.826 2.226
                arcTo(10.228f, 10.228f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 12.826f, 2.226f)
                // A 8.024 8.024 0 0 1 18.609 0.203
                arcTo(8.024f, 8.024f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 18.609f, 0.203f)
                // Q 21.559 0.203 23.334 1.828
                quadTo(21.559f, 0.203f, 23.334f, 1.828f)
                // Q 25.109 3.453 25.109 6.403
                quadTo(25.109f, 3.453f, 25.109f, 6.403f)
                // A 11.155 11.155 0 0 1 19.746 15.663
                arcTo(11.155f, 11.155f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 19.746f, 15.663f)
                // A 25.022 25.022 0 0 1 15.959 17.853
                arcTo(25.022f, 25.022f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 15.959f, 17.853f)
                // L 14.459 29.653
                lineTo(14.459f, 29.653f)
                // L 14.409 30.153
                lineTo(14.409f, 30.153f)
                // A 45.646 45.646 0 0 0 17.939 33.067
                arcTo(45.646f, 45.646f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 17.939f, 33.067f)
                // A 6.593 6.593 0 0 0 19.359 33.903
                arcTo(6.593f, 6.593f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 19.359f, 33.903f)
                // Q 19.909 34.103 20.584 34.103
                quadTo(19.909f, 34.103f, 20.584f, 34.103f)
                // Q 21.259 34.103 21.884 33.953
                quadTo(21.259f, 34.103f, 21.884f, 33.953f)
                // Q 22.509 33.803 23.034 33.228
                quadTo(22.509f, 33.803f, 23.034f, 33.228f)
                // Q 23.559 32.653 23.859 32.303
                quadTo(23.559f, 32.653f, 23.859f, 32.303f)
                // Q 24.159 31.953 24.609 30.753
                quadTo(24.159f, 31.953f, 24.609f, 30.753f)
                // A 63.125 63.125 0 0 0 25.038 29.587
                arcTo(63.125f, 63.125f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 25.038f, 29.587f)
                // L 26.609 16.103
                lineTo(26.609f, 16.103f)
                // Q 27.109 15.203 28.509 14.728
                quadTo(27.109f, 15.203f, 28.509f, 14.728f)
                // Q 29.909 14.253 31.284 14.253
                quadTo(29.909f, 14.253f, 31.284f, 14.253f)
                // Q 32.659 14.253 33.709 14.603
                quadTo(32.659f, 14.253f, 33.709f, 14.603f)
                // Q 32.216 26.126 31.867 28.915
                quadTo(32.216f, 26.126f, 31.867f, 28.915f)
                // A 37.509 37.509 0 0 0 31.759 29.803
                arcTo(37.509f, 37.509f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 31.759f, 29.803f)
                // Q 31.509 30.803 31.509 31.478
                quadTo(31.509f, 30.803f, 31.509f, 31.478f)
                // Q 31.509 32.153 31.609 32.653
                quadTo(31.509f, 32.153f, 31.609f, 32.653f)
                // Q 31.809 33.953 33.434 33.953
                quadTo(31.809f, 33.953f, 33.434f, 33.953f)
                // Q 35.059 33.953 36.434 31.828
                quadTo(35.059f, 33.953f, 36.434f, 31.828f)
                // A 16.376 16.376 0 0 0 36.838 31.161
                arcTo(16.376f, 16.376f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 36.838f, 31.161f)
                // L 38.759 14.703
                lineTo(38.759f, 14.703f)
                // Q 40.859 14.153 41.959 14.153
                quadTo(40.859f, 14.153f, 41.959f, 14.153f)
                // Q 45.309 14.153 45.459 16.603
                quadTo(45.309f, 14.153f, 45.459f, 16.603f)
                // Q 47.909 14.203 51.334 14.203
                quadTo(47.909f, 14.203f, 51.334f, 14.203f)
                // Q 54.759 14.203 56.709 15.903
                quadTo(54.759f, 14.203f, 56.709f, 15.903f)
                // Q 58.659 17.603 58.659 20.203
                quadTo(58.659f, 17.603f, 58.659f, 20.203f)
                // Q 58.659 20.903 58.359 22.853
                quadTo(58.659f, 20.903f, 58.359f, 22.853f)
                // A 339.523 339.523 0 0 0 57.573 28.288
                arcTo(339.523f, 339.523f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 57.573f, 28.288f)
                // Q 57.3 30.322 57.232 31.226
                quadTo(57.3f, 30.322f, 57.232f, 31.226f)
                // A 6.699 6.699 0 0 0 57.209 31.703
                arcTo(6.699f, 6.699f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 57.209f, 31.703f)
                // Q 57.209 34.003 58.759 34.003
                quadTo(57.209f, 34.003f, 58.759f, 34.003f)
                // A 4.391 4.391 0 0 0 62.229 31.61
                arcTo(4.391f, 4.391f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 62.229f, 31.61f)
                // A 20.35 20.35 0 0 1 62.459 29.553
                arcTo(20.35f, 20.35f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 62.459f, 29.553f)
                // Q 63.609 20.253 64.209 14.753
                quadTo(63.609f, 20.253f, 64.209f, 14.753f)
                // Q 65.759 14.203 67.059 14.203
                quadTo(65.759f, 14.203f, 67.059f, 14.203f)
                // Q 68.359 14.203 69.009 14.303
                quadTo(68.359f, 14.203f, 69.009f, 14.303f)
                // Q 69.659 14.403 70.259 15.053
                quadTo(69.659f, 14.403f, 70.259f, 15.053f)
                // Q 70.859 15.703 70.859 16.903
                quadTo(70.859f, 15.703f, 70.859f, 16.903f)
                // L 69.209 29.653
                lineTo(69.209f, 29.653f)
                // Q 69.109 30.253 69.109 30.753
                quadTo(69.109f, 30.253f, 69.109f, 30.753f)
                // Q 69.109 33.703 71.809 33.703
                quadTo(69.109f, 33.703f, 71.809f, 33.703f)
                // Q 74.309 33.703 75.959 31.253
                quadTo(74.309f, 33.703f, 75.959f, 31.253f)
                // Q 75.959 30.703 76.059 29.503
                quadTo(75.959f, 30.703f, 76.059f, 29.503f)
                // L 77.709 16.203
                lineTo(77.709f, 16.203f)
                // Q 78.059 15.353 79.209 14.778
                quadTo(78.059f, 15.353f, 79.209f, 14.778f)
                // Q 80.359 14.203 82.084 14.203
                quadTo(80.359f, 14.203f, 82.084f, 14.203f)
                // Q 83.809 14.203 84.659 14.703
                quadTo(83.809f, 14.203f, 84.659f, 14.703f)
                // Q 82.97 28.056 82.673 31.116
                quadTo(82.97f, 28.056f, 82.673f, 31.116f)
                // A 11.664 11.664 0 0 0 82.609 31.953
                arcTo(11.664f, 11.664f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 82.609f, 31.953f)
                // Q 82.609 33.953 84.059 33.953
                quadTo(82.609f, 33.953f, 84.059f, 33.953f)
                // Q 85.359 33.953 86.884 32.178
                quadTo(85.359f, 33.953f, 86.884f, 32.178f)
                // A 13.044 13.044 0 0 0 89.295 26.909
                arcTo(13.044f, 13.044f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 89.295f, 26.909f)
                // A 25.705 25.705 0 0 0 89.309 26.853
                arcTo(25.705f, 25.705f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 89.309f, 26.853f)
                // A 10.049 10.049 0 0 1 90.998 29.046
                arcTo(10.049f, 10.049f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 90.998f, 29.046f)
                // A 7.625 7.625 0 0 1 93.209 28.703
                arcTo(7.625f, 7.625f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 93.209f, 28.703f)
                // Q 93.909 28.703 94.659 28.853
                quadTo(93.909f, 28.703f, 94.659f, 28.853f)
                // L 95.859 27.053
                lineTo(95.859f, 27.053f)
                // A 1404.005 1404.005 0 0 0 93.331 22.505
                arcTo(1404.005f, 1404.005f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 93.331f, 22.505f)
                // Q 92.106 20.313 91.909 20.003
                quadTo(92.106f, 20.313f, 91.909f, 20.003f)
                // Q 91.309 19.153 90.784 19.153
                quadTo(91.309f, 19.153f, 90.784f, 19.153f)
                // Q 90.259 19.153 89.909 19.528
                quadTo(90.259f, 19.153f, 89.909f, 19.528f)
                // Q 89.559 19.903 89.559 20.553
                quadTo(89.559f, 19.903f, 89.559f, 20.553f)
                // Q 89.559 21.203 89.759 22.003
                quadTo(89.559f, 21.203f, 89.759f, 22.003f)
                // L 88.509 22.353
                lineTo(88.509f, 22.353f)
                // Q 88.059 21.753 87.759 20.628
                quadTo(88.059f, 21.753f, 87.759f, 20.628f)
                // Q 87.459 19.503 87.459 18.853
                quadTo(87.459f, 19.503f, 87.459f, 18.853f)
                // Q 87.459 16.403 89.009 15.028
                quadTo(87.459f, 16.403f, 89.009f, 15.028f)
                // Q 90.559 13.653 93.059 13.653
                quadTo(90.559f, 13.653f, 93.059f, 13.653f)
                // Q 96.109 13.653 97.809 16.953
                quadTo(96.109f, 13.653f, 97.809f, 16.953f)
                // L 99.859 20.903
                lineTo(99.859f, 20.903f)
                // L 102.609 16.603
                lineTo(102.609f, 16.603f)
                // Q 103.809 14.753 104.809 14.128
                quadTo(103.809f, 14.753f, 104.809f, 14.128f)
                // Q 105.809 13.503 107.384 13.503
                quadTo(105.809f, 13.503f, 107.384f, 13.503f)
                // Q 108.959 13.503 110.109 14.628
                quadTo(108.959f, 13.503f, 110.109f, 14.628f)
                // Q 111.259 15.753 111.259 17.303
                quadTo(111.259f, 15.753f, 111.259f, 17.303f)
                // Q 111.259 19.953 109.359 21.403
                quadTo(111.259f, 19.953f, 109.359f, 21.403f)
                // Q 107.459 22.853 105.159 22.853
                quadTo(107.459f, 22.853f, 105.159f, 22.853f)
                // Q 104.709 22.853 104.009 22.753
                quadTo(104.709f, 22.853f, 104.009f, 22.753f)
                // L 102.209 25.403
                lineTo(102.209f, 25.403f)
                // A 4174.136 4174.136 0 0 0 104.601 30.04
                arcTo(4174.136f, 4174.136f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 104.601f, 30.04f)
                // Q 105.472 31.726 105.801 32.353
                quadTo(105.472f, 31.726f, 105.801f, 32.353f)
                // A 33.953 33.953 0 0 0 105.959 32.653
                arcTo(33.953f, 33.953f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 105.959f, 32.653f)
                // Q 106.709 34.003 107.859 34.003
                quadTo(106.709f, 34.003f, 107.859f, 34.003f)
                // A 2.861 2.861 0 0 0 110.187 31.606
                arcTo(2.861f, 2.861f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 110.187f, 31.606f)
                // A 10.453 10.453 0 0 0 110.309 31.253
                arcTo(10.453f, 10.453f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 110.309f, 31.253f)
                // L 110.509 30.653
                lineTo(110.509f, 30.653f)
                // Q 110.559 30.503 110.759 29.653
                quadTo(110.559f, 30.503f, 110.759f, 29.653f)
                // L 112.459 32.103
                lineTo(112.459f, 32.103f)
                // Q 111.659 35.203 109.909 36.878
                quadTo(111.659f, 35.203f, 109.909f, 36.878f)
                // Q 108.159 38.553 105.784 38.553
                quadTo(108.159f, 38.553f, 105.784f, 38.553f)
                // Q 103.409 38.553 102.134 37.603
                quadTo(103.409f, 38.553f, 102.134f, 37.603f)
                // Q 100.859 36.653 99.759 34.403
                quadTo(100.859f, 36.653f, 99.759f, 34.403f)
                // L 98.159 31.303
                lineTo(98.159f, 31.303f)
                // L 95.709 34.853
                lineTo(95.709f, 34.853f)
                // A 7.592 7.592 0 0 1 91.717 38.172
                arcTo(7.592f, 7.592f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 91.717f, 38.172f)
                // A 3.946 3.946 0 0 1 90.709 38.303
                arcTo(3.946f, 3.946f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 90.709f, 38.303f)
                // Q 88.709 38.303 87.634 37.178
                quadTo(88.709f, 38.303f, 87.634f, 37.178f)
                // A 3.8 3.8 0 0 1 87.307 36.793
                arcTo(3.8f, 3.8f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 87.307f, 36.793f)
                // A 8.173 8.173 0 0 1 82.509 38.503
                arcTo(8.173f, 8.173f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 82.509f, 38.503f)
                // Q 78.159 38.503 76.659 35.153
                quadTo(78.159f, 38.503f, 76.659f, 35.153f)
                // Q 74.059 38.503 69.709 38.503
                quadTo(74.059f, 38.503f, 69.709f, 38.503f)
                // Q 66.109 38.503 64.159 36.703
                quadTo(66.109f, 38.503f, 64.159f, 36.703f)
                // A 6.099 6.099 0 0 1 63.128 35.455
                arcTo(6.099f, 6.099f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 63.128f, 35.455f)
                // A 15.38 15.38 0 0 1 62.583 36.106
                arcTo(15.38f, 15.38f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 62.583f, 36.106f)
                // A 7.79 7.79 0 0 1 56.759 38.553
                arcTo(7.79f, 7.79f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 56.759f, 38.553f)
                // Q 53.659 38.553 52.159 36.653
                quadTo(53.659f, 38.553f, 52.159f, 36.653f)
                // Q 50.659 34.753 50.659 32.653
                quadTo(50.659f, 34.753f, 50.659f, 32.653f)
                // L 51.859 21.503
                lineTo(51.859f, 21.503f)
                // Q 51.859 20.153 51.159 19.253
                quadTo(51.859f, 20.153f, 51.159f, 19.253f)
                // Q 50.459 18.353 48.834 18.353
                quadTo(50.459f, 18.353f, 48.834f, 18.353f)
                // Q 47.209 18.353 46.084 19.778
                quadTo(47.209f, 18.353f, 46.084f, 19.778f)
                // Q 44.959 21.203 44.659 23.353
                quadTo(44.959f, 21.203f, 44.659f, 23.353f)
                // A 4624.422 4624.422 0 0 0 43.866 29.422
                arcTo(4624.422f, 4624.422f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 43.866f, 29.422f)
                // Q 42.834 37.353 42.759 38.253
                quadTo(42.834f, 37.353f, 42.759f, 38.253f)
                // L 36.009 38.253
                lineTo(36.009f, 38.253f)
                // L 36.154 37.015
                lineTo(36.154f, 37.015f)
                // A 7.765 7.765 0 0 1 31.359 38.603
                arcTo(7.765f, 7.765f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 31.359f, 38.603f)
                // Q 28.009 38.603 26.384 36.953
                quadTo(28.009f, 38.603f, 26.384f, 36.953f)
                // A 5.8 5.8 0 0 1 25.565 35.879
                arcTo(5.8f, 5.8f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 25.565f, 35.879f)
                // A 16.014 16.014 0 0 1 25.503 35.959
                arcTo(16.014f, 16.014f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 25.503f, 35.959f)
                // A 6.929 6.929 0 0 1 20.009 38.553
                arcTo(6.929f, 6.929f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 20.009f, 38.553f)
                // A 8.977 8.977 0 0 1 15.358 36.805
                arcTo(8.977f, 8.977f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 15.358f, 36.805f)
                // A 26.611 26.611 0 0 1 12.759 34.853
                arcTo(26.611f, 26.611f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 12.759f, 34.853f)
                // Q 10.459 38.553 5.609 38.553
                quadTo(10.459f, 38.553f, 5.609f, 38.553f)
                // Q 3.459 38.553 1.734 37.103
                quadTo(3.459f, 38.553f, 1.734f, 37.103f)
                // Q 0.009 35.653 0.009 32.953
                quadTo(0.009f, 35.653f, 0.009f, 32.953f)
                // Q 0.009 30.253 1.809 28.528
                quadTo(0.009f, 30.253f, 1.809f, 28.528f)
                // Q 3.609 26.803 6.459 26.803
                quadTo(3.609f, 26.803f, 6.459f, 26.803f)
                // Q 7.009 26.803 8.009 26.903 (这个点接近第一个M点，可能表示闭合或连续)
                quadTo(7.009f, 26.803f, 8.009f, 26.903f)


                // M 131.009 7.303 (新的子路径开始)
                moveTo(131.009f, 7.303f)
                // L 127.859 28.653
                lineTo(127.859f, 28.653f)
                // Q 127.409 31.503 126.609 33.403
                quadTo(127.409f, 31.503f, 126.609f, 33.403f)
                // Q 128.159 33.803 129.109 33.803
                quadTo(128.159f, 33.803f, 129.109f, 33.803f)
                // Q 134.859 33.803 138.009 29.453
                quadTo(134.859f, 33.803f, 138.009f, 29.453f)
                // Q 141.259 24.903 141.259 18.453
                quadTo(141.259f, 24.903f, 141.259f, 18.453f)
                // A 13.895 13.895 0 0 0 138.194 9.006
                arcTo(13.895f, 13.895f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 138.194f, 9.006f)
                // A 12.055 12.055 0 0 0 135.309 6.603
                arcTo(12.055f, 12.055f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 135.309f, 6.603f)
                // Q 132.009 4.603 128.059 4.603
                quadTo(132.009f, 4.603f, 128.059f, 4.603f)
                // Q 123.009 4.603 120.059 7.353
                quadTo(123.009f, 4.603f, 120.059f, 7.353f)
                // Q 118.359 8.903 118.359 11.103
                quadTo(118.359f, 8.903f, 118.359f, 11.103f)
                // Q 118.359 12.253 119.059 13.278
                quadTo(118.359f, 12.253f, 119.059f, 13.278f)
                // Q 119.759 14.303 121.509 14.453
                quadTo(119.759f, 14.303f, 121.509f, 14.453f)
                // L 121.009 17.703
                lineTo(121.009f, 17.703f)
                // Q 117.359 17.503 115.584 15.678
                quadTo(117.359f, 17.503f, 115.584f, 15.678f)
                // Q 113.809 13.853 113.809 11.453
                quadTo(113.809f, 13.853f, 113.809f, 11.453f)
                // Q 113.809 6.103 118.459 3.053
                quadTo(113.809f, 6.103f, 118.459f, 3.053f)
                // Q 123.109 0.003 129.159 0.003
                quadTo(123.109f, 0.003f, 129.159f, 0.003f)
                // Q 136.909 0.003 142.759 4.453
                quadTo(136.909f, 0.003f, 142.759f, 4.453f)
                // Q 145.659 6.703 147.259 10.428
                quadTo(145.659f, 6.703f, 147.259f, 10.428f)
                // Q 148.859 14.153 148.859 18.653
                quadTo(148.859f, 14.153f, 148.859f, 18.653f)
                // Q 148.859 27.503 143.809 33.053
                quadTo(148.859f, 27.503f, 143.809f, 33.053f)
                // Q 138.559 38.703 130.659 38.703
                quadTo(138.559f, 38.703f, 130.659f, 38.703f)
                // Q 128.909 38.703 126.959 38.253
                quadTo(128.909f, 38.703f, 126.959f, 38.253f)
                // Q 125.009 37.803 123.859 37.053
                quadTo(125.009f, 37.803f, 123.859f, 37.053f)
                // Q 122.009 38.453 119.134 38.453
                quadTo(122.009f, 38.453f, 119.134f, 38.453f)
                // Q 116.259 38.453 114.659 36.853
                quadTo(116.259f, 38.453f, 114.659f, 36.853f)
                // Q 113.059 35.253 113.059 32.753
                quadTo(113.059f, 35.253f, 113.059f, 32.753f)
                // Q 113.059 26.853 122.009 22.703
                quadTo(113.059f, 26.853f, 122.009f, 22.703f)
                // A 422.9 422.9 0 0 1 122.676 17.469
                arcTo(422.9f, 422.9f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 122.676f, 17.469f)
                // A 870.258 870.258 0 0 1 122.734 17.028
                arcTo(870.258f, 870.258f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 122.734f, 17.028f)
                // Q 123.209 13.453 123.484 11.253
                quadTo(123.209f, 13.453f, 123.484f, 11.253f)
                // A 236.285 236.285 0 0 1 123.846 8.447
                arcTo(236.285f, 236.285f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 123.846f, 8.447f)
                // A 31.177 31.177 0 0 1 123.859 8.353
                arcTo(31.177f, 31.177f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 123.859f, 8.353f)
                // Q 125.909 7.053 128.609 7.053
                quadTo(125.909f, 7.053f, 128.609f, 7.053f)
                // Q 129.859 7.053 131.009 7.303
                quadTo(129.859f, 7.053f, 131.009f, 7.303f)


                // M 163.509 14.203 (新的子路径开始)
                moveTo(163.509f, 14.203f)
                // Q 168.109 14.203 171.184 17.153
                quadTo(168.109f, 14.203f, 171.184f, 17.153f)
                // Q 174.259 20.103 174.259 24.353
                quadTo(174.259f, 20.103f, 174.259f, 24.353f)
                // Q 174.259 30.803 170.484 34.678
                quadTo(174.259f, 30.803f, 170.484f, 34.678f)
                // Q 166.709 38.553 161.359 38.553
                quadTo(166.709f, 38.553f, 161.359f, 38.553f)
                // Q 156.009 38.553 152.884 35.503
                quadTo(156.009f, 38.553f, 152.884f, 35.503f)
                // Q 149.759 32.453 149.759 27.953
                quadTo(149.759f, 32.453f, 149.759f, 27.953f)
                // Q 149.759 21.653 153.609 17.953
                quadTo(149.759f, 21.653f, 153.609f, 17.953f)
                // Q 157.509 14.203 163.509 14.203
                quadTo(157.509f, 14.203f, 163.509f, 14.203f)


                // M 162.534 18.253 (新的子路径开始)
                moveTo(162.534f, 18.253f)
                // Q 160.159 18.253 158.559 20.953
                quadTo(160.159f, 18.253f, 158.559f, 20.953f)
                // Q 156.959 23.653 156.809 27.753
                quadTo(156.959f, 23.653f, 156.809f, 27.753f)
                // Q 156.809 30.653 157.984 32.553
                quadTo(156.809f, 30.653f, 157.984f, 32.553f)
                // Q 159.159 34.453 161.559 34.453
                quadTo(159.159f, 34.453f, 161.559f, 34.453f)
                // Q 163.959 34.453 165.584 31.803
                quadTo(163.959f, 34.453f, 165.584f, 31.803f)
                // Q 167.209 29.153 167.209 25.753
                quadTo(167.209f, 29.153f, 167.209f, 25.753f)
                // Q 167.209 22.353 166.059 20.353
                quadTo(167.209f, 22.353f, 166.059f, 20.353f)
                // Q 164.909 18.253 162.534 18.253
                quadTo(164.909f, 18.253f, 162.534f, 18.253f)


                // M 28.659 1.428 (新的子路径开始)
                moveTo(28.659f, 1.428f)
                // Q 29.809 0.253 31.459 0.253
                quadTo(29.809f, 0.253f, 31.459f, 0.253f)
                // Q 33.109 0.253 34.284 1.428
                quadTo(33.109f, 0.253f, 34.284f, 1.428f)
                // Q 35.459 2.603 35.459 4.253
                quadTo(35.459f, 2.603f, 35.459f, 4.253f)
                // Q 35.459 5.903 34.284 7.053
                quadTo(35.459f, 5.903f, 34.284f, 7.053f)
                // Q 33.109 8.203 31.459 8.203
                quadTo(33.109f, 8.203f, 31.459f, 8.203f)
                // Q 29.809 8.203 28.659 7.053
                quadTo(29.809f, 8.203f, 28.659f, 7.053f)
                // Q 27.509 5.903 27.509 4.253
                quadTo(27.509f, 5.903f, 27.509f, 4.253f)
                // Q 27.509 2.603 28.659 1.428
                quadTo(27.509f, 2.603f, 28.659f, 1.428f)


                // M 16.959 9.653 (新的子路径开始)
                moveTo(16.959f, 9.653f)
                // L 16.459 13.603
                lineTo(16.459f, 13.603f)
                // Q 21.359 10.703 21.359 6.803
                quadTo(21.359f, 10.703f, 21.359f, 6.803f)
                // Q 21.359 4.703 19.609 4.703
                quadTo(21.359f, 4.703f, 19.609f, 4.703f)
                // A 2.643 2.643 0 0 0 17.423 7.151
                arcTo(2.643f, 2.643f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 17.423f, 7.151f)
                // A 15.088 15.088 0 0 0 16.959 9.653
                arcTo(15.088f, 15.088f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 16.959f, 9.653f)


                // M 121.009 30.053 (新的子路径开始)
                moveTo(121.009f, 30.053f)
                // L 121.459 26.953
                lineTo(121.459f, 26.953f)
                // Q 116.759 29.603 116.759 32.103
                quadTo(116.759f, 29.603f, 116.759f, 32.103f)
                // Q 116.759 33.903 118.459 33.903
                quadTo(116.759f, 33.903f, 118.459f, 33.903f)
                // Q 119.159 33.903 119.934 32.978
                quadTo(119.159f, 33.903f, 119.934f, 32.978f)
                // Q 120.709 32.053 121.009 30.053
                quadTo(120.709f, 32.053f, 121.009f, 30.053f)


                // M 3.809 32.603 (新的子路径开始)
                moveTo(3.809f, 32.603f)
                // Q 3.809 34.103 5.509 34.103
                quadTo(3.809f, 34.103f, 5.509f, 34.103f)
                // A 2.369 2.369 0 0 0 7.489 31.596
                arcTo(2.369f, 2.369f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 7.489f, 31.596f)
                // A 9.712 9.712 0 0 0 7.559 31.153
                arcTo(9.712f, 9.712f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 7.559f, 31.153f)
                // Q 6.609 30.753 5.784 30.753
                quadTo(6.609f, 30.753f, 5.784f, 30.753f)
                // Q 4.959 30.753 4.384 31.303
                quadTo(4.959f, 30.753f, 4.384f, 31.303f)
                // Q 3.809 31.853 3.809 32.603
                quadTo(3.809f, 31.853f, 3.809f, 32.603f)


                // M 89.587 34.162 (新的子路径开始)
                moveTo(89.587f, 34.162f)
                // A 13.747 13.747 0 0 0 90.951 31.1
                arcTo(13.747f, 13.747f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 90.951f, 31.1f)
                // A 5.623 5.623 0 0 1 93.009 30.703
                arcTo(5.623f, 5.623f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 93.009f, 30.703f)
                // L 93.409 30.703
                lineTo(93.409f, 30.703f)
                // A 24.925 24.925 0 0 1 91.769 32.836
                arcTo(24.925f, 24.925f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 91.769f, 32.836f)
                // A 3.286 3.286 0 0 1 89.909 34.203
                arcTo(3.286f, 3.286f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 89.909f, 34.203f)
                // A 0.868 0.868 0 0 1 89.587 34.162
                arcTo(0.868f, 0.868f, 0.0f, isMoreThanHalf = false, isPositiveArc = true, 89.587f, 34.162f)


                // M 108.159 17.353 (新的子路径开始)
                moveTo(108.159f, 17.353f)
                // A 1.199 1.199 0 0 0 107.234 17.878
                arcTo(1.199f, 1.199f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 107.234f, 17.878f)
                // Q 106.809 18.403 105.259 20.853
                quadTo(106.809f, 18.403f, 105.259f, 20.853f)
                // Q 106.609 20.853 107.809 20.178
                quadTo(106.609f, 20.853f, 107.809f, 20.178f)
                // Q 109.009 19.503 109.059 18.428
                quadTo(109.009f, 19.503f, 109.059f, 18.428f)
                // A 2.257 2.257 0 0 0 109.062 18.324
                arcTo(2.257f, 2.257f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 109.062f, 18.324f)
                // A 0.829 0.829 0 0 0 108.159 17.353
                arcTo(0.829f, 0.829f, 0.0f, isMoreThanHalf = false, isPositiveArc = false, 108.159f, 17.353f)
            }
        }.build()
        return _myManuallyConvertedSvgIcon!!
    }

private var _myManuallyConvertedSvgIcon: ImageVector? = null

@Preview
@Composable
fun MyComposableScreen() {
    Icon(
        imageVector = MyManuallyConvertedSvgIcon, // 使用您刚刚创建的ImageVector
        contentDescription = "我的手动转换SVG图标", // 提供一个无障碍描述
        modifier = Modifier.size(200.dp) // 可以按需调整图标大小
    )
}

