package cn.wantu.linuxdo.ui.view

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import cn.wantu.linuxdo.ui.icon.LinuxDoIcon
import cn.wantu.linuxdo.ui.model.ForumLoginViewModel
import cn.wantu.linuxdo.ui.theme.LinuxDoTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForumLoginScreen(
    viewModel: ForumLoginViewModel = viewModel() // 获取ViewModel实例
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box(modifier = Modifier.padding(6.dp).size(36.dp).LinuxDoIcon())
                        Text("LinuxDo Login")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
                    .imePadding() // 适应键盘弹出时的布局
                    .navigationBarsPadding(), // 适应底部导航栏
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 欢迎标题
                Text(
                    text = "欢迎登录",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                // 用户名/邮箱输入框
                OutlinedTextField(
                    value = viewModel.username,
                    onValueChange = viewModel::onUsernameChange,
                    label = { Text("用户名或邮箱") },
                    leadingIcon = { Icon(Icons.Default.Person, contentDescription = "用户名图标") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))


                // 密码输入框
                OutlinedTextField(
                    value = viewModel.password,
                    onValueChange = viewModel::onPasswordChange,
                    label = { Text("密码") },
                    leadingIcon = { Icon(Icons.Default.Lock, contentDescription = "密码图标") },
                    visualTransformation = if (viewModel.showPassword) VisualTransformation.None else PasswordVisualTransformation(), // 密码显示为星号
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    trailingIcon = {
                        // 可点击的图标按钮
                        IconButton(
                            onClick = viewModel::togglePasswordVisibility // 点击时切换 showPassword 状态
                        ) {
                            // 根据 showPassword 状态选择不同的图标
                           /* Icon(
                                imageVector = if (viewModel.showPassword) Icons.Filled.Visibility else Icons.Filled.VisibilityOff,
                                contentDescription = if (viewModel.showPassword) "隐藏密码" else "显示密码"
                            )*/
                        }
                    }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 记住我 & 忘记密码
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Checkbox(
                            checked = viewModel.rememberMe,
                            onCheckedChange = viewModel::onRememberMeChange
                        )
                        Text(text = "记住我")
                    }
                    Text(
                        text = "忘记密码?",
                        color = MaterialTheme.colorScheme.primary,
                        textDecoration = TextDecoration.Underline,
                        modifier = Modifier.clickable { viewModel.onForgotPasswordClick() }
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 登录按钮
                Button(
                    onClick = viewModel::onLoginClick,
                    enabled = !viewModel.isLoading, // 正在加载时禁用按钮
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp)
                ) {
                    if (viewModel.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = MaterialTheme.colorScheme.onPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("登录", style = MaterialTheme.typography.titleMedium)
                    }
                }



                Spacer(modifier = Modifier.height(32.dp))

                // 注册链接
                Text(
                    text = buildAnnotatedString {
                        append("没有账号？")
                        withStyle(
                            style = SpanStyle(
                                color = MaterialTheme.colorScheme.primary,
                                textDecoration = TextDecoration.Underline
                            )
                        ) {
                            append("立即注册")
                        }
                    },
                    modifier = Modifier.clickable { viewModel.onRegisterClick() }
                )
            }
            NotificationQueueDisplay(
                notifications = viewModel.notifications,
                onDismiss = viewModel::dismissNotification
            )
        }
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun PreviewForumLoginScreen() {
    LinuxDoTheme { // 确保你的主题是正确的
        ForumLoginScreen()
    }
}