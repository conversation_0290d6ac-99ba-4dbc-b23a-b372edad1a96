package cn.wantu.linuxdo.ui.view

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

open class Notification(
    val message: String,
    val backgroundColor: Color,
    val textColor: Color,
    val duration: Long = 3000, // 3秒
    val icon: @Composable () -> Unit = {
        Icon(
            imageVector = Icons.Default.Notifications,
            contentDescription = "通知",
            modifier = Modifier.size(20.dp)
        )
    }
)

class SuccessNotification(message: String) : Notification(
    message = message,
    backgroundColor = Color(0xFF69F0AE),
    textColor = Color.White,
    icon = {
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = "成功",
            modifier = Modifier.size(20.dp)
        )
    })

class ErrorNotification(message: String) :
    Notification(message = message, backgroundColor = Color.Red, textColor = Color.White)

@Composable
fun NotificationQueueDisplay(
    notifications: List<Notification>,
    onDismiss: (Notification) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val spacing = 0.dp
    val animationDuration = 400

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        notifications.takeLast(3).asReversed().forEachIndexed { index, notification ->
            println("index: $index, notification: $notification")
            val isTop = index == 0
            val verticalOffset by animateDpAsState(
                targetValue = if (isTop) 0.dp else (index * 48).dp + (spacing * index),
                animationSpec = tween(
                    easing = FastOutSlowInEasing
                ),
                label = "notification_offset"
            )

            LaunchedEffect(notification) {
                println("I was called")
                delay(notification.duration)
                onDismiss(notification)
            }

            AnimatedVisibility(
                visible = true,
                enter = slideInVertically { with(density) { -40.dp.roundToPx() } } +
                        fadeIn(animationSpec = tween(durationMillis = animationDuration)),
                exit = slideOutVertically { with(density) { -40.dp.roundToPx() } } +
                        fadeOut(animationSpec = tween(durationMillis = animationDuration)),
                modifier = Modifier
                    .offset(y = verticalOffset)
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = if (isTop) 8.dp else 4.dp,
                            shape = MaterialTheme.shapes.medium,
                            spotColor = MaterialTheme.colorScheme.outline
                        ),
                    colors = CardDefaults.cardColors(
                        containerColor = notification.backgroundColor,
                        contentColor = notification.textColor
                    ),
                    shape = MaterialTheme.shapes.medium,
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        notification.icon()
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = notification.message,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        IconButton(
                            onClick = { onDismiss(notification) },
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}