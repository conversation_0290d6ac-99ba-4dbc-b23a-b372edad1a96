package cn.wantu.linuxdo

import android.app.Application
import android.content.Context
import android.util.Log
import cn.wantu.linuxdo.network.OkHttpClientSingleton

/**
 * 自定义 Application 类
 * 用于在应用程序启动时进行全局初始化和管理全局状态。
 */
class App : Application() {

    private val TAG = "App" // 用于日志输出的 TAG

    // 假设这是一个用于存储全局数据的对象
    // 你可以使用 Kotlin 的 object 关键字轻松实现单例
    companion object {

        lateinit var instance: App
            private set
    }

    /**
     * 当应用程序进程创建时，此方法会被调用。
     * 它是应用程序生命周期中最早被调用的方法之一。
     * 适合进行全局性的初始化工作。
     */
    override fun onCreate() {
        super.onCreate() // 始终调用父类的 onCreate() 方法
        instance = this // 设置全局实例

        OkHttpClientSingleton.init(this)

        Log.d(TAG, "Application initialization complete.")
    }

    /**
     * 当应用程序内存不足时，系统会调用此方法。
     * 可以在这里释放一些不必要的内存资源。
     */
    override fun onLowMemory() {
        super.onLowMemory()
        Log.w(TAG, "onLowMemory() called. Clearing memory caches...")
        // 清理缓存、释放资源等
    }

    /**
     * 当应用程序进程终止时，此方法会被调用。
     * **注意：** 此方法不保证一定会被调用，尤其是在系统强制终止应用进程以回收内存时。
     * 不应依赖此方法进行关键数据的持久化操作。
     */
    override fun onTerminate() {
        super.onTerminate()
        Log.d(TAG, "Application onTerminate() called. (Note: Not guaranteed to be called!)")
        // 进行一些清理工作，但不要依赖它
    }

}