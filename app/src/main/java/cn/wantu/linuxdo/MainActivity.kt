package cn.wantu.linuxdo

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.lifecycle.lifecycleScope
import cn.wantu.linuxdo.config.getCsrf
import cn.wantu.linuxdo.ui.theme.LinuxDoTheme
import cn.wantu.linuxdo.ui.view.ForumLoginScreen
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LinuxDoTheme {
                ForumLoginScreen()
            }
        }

        lifecycleScope.launch {
            getCsrf()
        }
    }
}
